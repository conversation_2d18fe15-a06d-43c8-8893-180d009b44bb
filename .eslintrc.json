{"env": {"node": true, "browser": true, "commonjs": true, "es2021": true}, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 13}, "rules": {"accessor-pairs": "error", "array-bracket-newline": "error", "array-bracket-spacing": ["error", "never"], "array-callback-return": "off", "array-element-newline": "off", "arrow-body-style": "off", "arrow-parens": ["error", "always"], "arrow-spacing": ["error", {"after": true, "before": true}], "block-scoped-var": "error", "block-spacing": "error", "brace-style": ["error", "1tbs"], "camelcase": "off", "capitalized-comments": "off", "class-methods-use-this": "error", "comma-dangle": "off", "comma-spacing": ["error", {"after": true, "before": false}], "comma-style": ["error", "last"], "complexity": "error", "computed-property-spacing": ["error", "never"], "consistent-return": "off", "consistent-this": "error", "curly": "off", "default-case": "error", "default-case-last": "error", "default-param-last": "error", "dot-location": ["error", "property"], "dot-notation": ["error", {"allowKeywords": true}], "eol-last": "error", "eqeqeq": "off", "func-call-spacing": "error", "func-name-matching": "error", "func-names": "off", "func-style": ["error", "declaration"], "function-call-argument-newline": ["error", "consistent"], "function-paren-newline": "off", "generator-star-spacing": "error", "grouped-accessor-pairs": "error", "guard-for-in": "error", "id-denylist": "error", "id-length": "off", "id-match": "error", "implicit-arrow-linebreak": "off", "indent": "off", "init-declarations": "off", "jsx-quotes": "error", "key-spacing": "error", "keyword-spacing": ["error", {"after": true, "before": true}], "line-comment-position": "off", "linebreak-style": ["error", "unix"], "lines-around-comment": "off", "lines-between-class-members": "error", "max-classes-per-file": "error", "max-depth": "error", "max-len": "off", "max-lines": "error", "max-lines-per-function": "error", "max-nested-callbacks": "error", "max-params": "error", "max-statements": "error", "max-statements-per-line": "error", "multiline-comment-style": "off", "multiline-ternary": ["error", "always-multiline"], "new-cap": "error", "new-parens": "error", "newline-per-chained-call": "error", "no-alert": "error", "no-array-constructor": "error", "no-await-in-loop": "error", "no-bitwise": "error", "no-caller": "error", "no-confusing-arrow": "off", "no-console": "off", "no-constructor-return": "error", "no-continue": "error", "no-div-regex": "error", "no-duplicate-imports": "error", "no-else-return": "off", "no-empty-function": "off", "no-eq-null": "error", "no-eval": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-label": "error", "no-extra-parens": "error", "no-floating-decimal": "error", "no-implicit-coercion": "error", "no-implicit-globals": "error", "no-implied-eval": "error", "no-inline-comments": "off", "no-invalid-this": "error", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-lonely-if": "error", "no-loop-func": "error", "no-magic-numbers": "off", "no-mixed-operators": "error", "no-multi-assign": "error", "no-multi-spaces": "error", "no-multi-str": "error", "no-multiple-empty-lines": "error", "no-negated-condition": "off", "no-nested-ternary": "error", "no-new": "error", "no-new-func": "error", "no-new-object": "error", "no-new-wrappers": "error", "no-octal-escape": "error", "no-param-reassign": "error", "no-plusplus": "error", "no-promise-executor-return": "error", "no-proto": "error", "no-restricted-exports": "error", "no-restricted-globals": "error", "no-restricted-imports": "error", "no-restricted-properties": "error", "no-restricted-syntax": "error", "no-return-assign": "error", "no-return-await": "error", "no-script-url": "error", "no-self-compare": "error", "no-sequences": "error", "no-shadow": "error", "no-tabs": "error", "no-template-curly-in-string": "error", "no-ternary": "off", "no-throw-literal": "error", "no-trailing-spaces": "error", "no-undef-init": "error", "no-undefined": "error", "no-underscore-dangle": "off", "no-unmodified-loop-condition": "error", "no-unneeded-ternary": ["error", {"defaultAssignment": true}], "no-unreachable-loop": "error", "no-unused-expressions": "error", "no-unused-private-class-members": "error", "no-use-before-define": "off", "no-useless-call": "error", "no-useless-computed-key": "error", "no-useless-concat": "error", "no-useless-constructor": "error", "no-useless-rename": "error", "no-useless-return": "error", "no-var": "off", "no-void": "error", "no-warning-comments": ["error", {"location": "start"}], "no-whitespace-before-property": "error", "nonblock-statement-body-position": ["error", "any"], "object-curly-newline": "error", "object-curly-spacing": ["error", "always"], "object-shorthand": "off", "one-var": "off", "one-var-declaration-per-line": "error", "operator-assignment": "error", "operator-linebreak": ["error", null], "padded-blocks": "off", "padding-line-between-statements": "error", "prefer-arrow-callback": "off", "prefer-const": "off", "prefer-destructuring": "off", "prefer-exponentiation-operator": "error", "prefer-named-capture-group": "error", "prefer-numeric-literals": "error", "prefer-object-has-own": "error", "prefer-object-spread": "error", "prefer-promise-reject-errors": "error", "prefer-regex-literals": "error", "prefer-rest-params": "error", "prefer-spread": "error", "prefer-template": "off", "quote-props": "off", "quotes": ["error", "double"], "radix": "error", "require-atomic-updates": "error", "require-await": "error", "require-unicode-regexp": "error", "rest-spread-spacing": ["error", "never"], "semi": "error", "semi-spacing": "error", "semi-style": ["error", "last"], "sort-imports": "error", "sort-keys": "off", "sort-vars": "off", "space-before-blocks": "error", "space-before-function-paren": "off", "space-in-parens": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": "error", "spaced-comment": "off", "strict": ["error", "never"], "switch-colon-spacing": "error", "symbol-description": "error", "template-curly-spacing": "error", "template-tag-spacing": "error", "unicode-bom": ["error", "never"], "vars-on-top": "error", "wrap-iife": "error", "wrap-regex": "error", "yield-star-spacing": "error", "yoda": ["error", "never"]}}