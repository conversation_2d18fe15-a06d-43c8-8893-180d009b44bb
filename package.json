{"name": "friendly-chat", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:working": "npx jest tests/basic.test.js tests/database-mocked.test.js tests/requestHandler-mocked.test.js tests/responseHandler-mocked.test.js --config='{\"testEnvironment\": \"node\", \"setupFilesAfterEnv\": []}'", "test:unit": "node tests/runTests.js unit", "test:integration": "node tests/runTests.js integration", "test:api": "node tests/runTests.js api", "test:socket": "node tests/runTests.js socket", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "start": "node index.js", "nodemon": "nodemon index.js", "ngrok": "ngrok http 3000", "upload": "upload.bat", "clear_jest": "jest --clear<PERSON>ache"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "coveragePathIgnorePatterns": ["/node_modules/"], "testTimeout": 30000, "collectCoverageFrom": ["scripts/**/*.js", "app.*.js", "google.config.js", "!scripts/DB/schemes/**/index.js", "!**/node_modules/**"], "forceExit": true, "detectOpenHandles": true, "maxWorkers": 1, "clearMocks": true, "resetMocks": true, "restoreMocks": true}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@google-cloud/storage": "^5.18.1", "body-parser": "^1.19.1", "dotenv": "^10.0.0", "express": "^4.17.2", "googleapis": "^150.0.1", "leo-profanity": "^1.5.0", "mongoose": "^6.1.6", "request": "^2.88.2", "socket.io": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-transform-runtime": "^7.16.8", "@babel/preset-env": "^7.16.8", "@babel/runtime": "^7.16.7", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.3", "babel-plugin-transform-runtime": "^6.23.0", "eslint": "^8.6.0", "jest": "^27.4.7", "mongodb-memory-server": "^10.1.4", "nock": "^13.1.1", "nodemon": "^2.0.15", "prettier": "^2.5.1", "socket.io-client": "^4.8.1", "supertest": "^6.1.6"}}