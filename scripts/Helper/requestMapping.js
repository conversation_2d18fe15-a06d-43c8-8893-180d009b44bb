module.exports.createMsg = function (msg, sender, filter) {
  return {
    sender,
    ...createMessage(msg, filter),
  };
};
module.exports.mapSettings = function (sender, settings) {
  return {
    sender,
    postback: { payload: "settings", settings },
  };
};

module.exports.createAttachmentMsg = function (sender, imageUrl) {
  return {
    sender,
    ...createAttachment(imageUrl),
  };
};
module.exports.createPostBackMsg = function (sender, payload) {
  return {
    sender,
    postback: { payload },
  };
};
module.exports.createPostBackID = function (sender, payload, id) {
  return {
    sender,
    postback: { payload, id },
  };
};
function createMessage(msg, filter) {
  if (msg && msg.text) {
    let text = filter.clean(msg.text);
    console.log(text);
    return { message: { text } };
  }
}
function createAttachment(imageUrl) {
  if (imageUrl)
    return {
      message: {
        attachments: [
          {
            type: "image",
            payload: {
              url: imageUrl,
            },
          },
        ],
      },
    };
}
module.exports.createSender = function (senderId, deviceId, channelId) {
  return {
    sessionId: senderId,
    channelId: channelId,
    deviceId: deviceId,
  };
};
