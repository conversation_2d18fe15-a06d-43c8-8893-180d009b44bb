const Waiting = require("./waiting");

exports.fetch = (sender) => {
  return Waiting.findOne({
    user: { $ne: sender, $nin: sender.blockList },
    blockList: { $nin: [sender] },
  })
    .populate({
      path: "user",
      populate: { path: "settings" },
    })
    .exec();
};
exports.add = (sender) => {
  return Waiting.findOneAndUpdate(
    { user: sender },
    { user: sender, blockList: sender.blockList },
    { upsert: true, new: true }
  )
    .populate("user")
    .exec();
};
exports.remove = (sender) => {
  return Waiting.deleteOne({ user: sender }).exec();
};
