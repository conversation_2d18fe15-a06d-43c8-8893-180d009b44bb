var mongoose = require("mongoose");
var Schema = mongoose.Schema;

var aschema = new Schema(
  {
    sessionId: String, //sessionID
    channelId: String,
    deviceId: String,
    name: String,
    gender: String,
    settings: { type: Schema.Types.ObjectId, ref: "Settings" },
    blockList: [{ type: Schema.Types.ObjectId, ref: "User" }],
    reportList: [{ type: Schema.Types.ObjectId, ref: "User" }],
    lastReported: {
      date: Date,
      count: Number,
    },
    banCount: { type: Number, default: 0 },
  },
  { timestamps: {} }
);
aschema.index(
  { updatedAt: 1 },
  {
    expireAfterSeconds: 30 * 24 * 60 * 60,
  }
);
module.exports = mongoose.model("User", aschema);
