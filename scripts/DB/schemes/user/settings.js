var mongoose = require("mongoose");
var Schema = mongoose.Schema;

var aschema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: "User" },
  name: String,
  gender: String,
  avatar: {
    accessory: String,
    clothing: String,
    clothingColor: String,
    eyebrows: String,
    eyes: String,
    facialHair: String,
    hair: String,
    hairColor: String,
    hat: String,
    mouth: String,
    skinTone: String,
  },
});

module.exports = mongoose.model("Settings", aschema);
