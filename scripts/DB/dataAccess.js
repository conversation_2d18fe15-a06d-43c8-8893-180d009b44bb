const ChattingController = require("./schemes/chatting/chatting.ctrl"),
  WaitingController = require("./schemes/waiting/waiting.ctrl");
exports.getReceiver = (sender) => {
  return ChattingController.find(sender)
    .then((chatting) => {
      if (chatting) {
        //get the recieptID
        let [reciever] = chatting.participants.filter((participant) => {
          if (participant.id != sender.id) return participant;
        });
        return reciever;
      } else {
        return exports.addUserToWaiting(sender);
      }
    })
    .catch((err) => {
      if (err.message == "OtherUserInactive")
        return exports.addUserToWaiting(sender);
    });
};
exports.removeFromChatting = (sender) => {
  return ChattingController.remove(sender).then((data) =>
    data ? data : sender
  );
};
exports.addUserToWaiting = (sender) => {
  return WaitingController.fetch(sender).then((waiting) => {
    if (waiting && waiting.user !== null) {
      // console.debug("adding user to chatting ");
      WaitingController.remove(waiting.user);
      ChattingController.create(sender, waiting.user);
      return { ...waiting.user.toObject(), justMatched: true };
    } else {
      // console.debug("adding user to waiting ");
      return WaitingController.add(sender).then((userAdded) => {
        return userAdded.user;
      });
    }
  });
};
