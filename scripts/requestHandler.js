const ChattingController = require("./DB/schemes/chatting/chatting.ctrl");
const UserController = require("./DB/schemes/user/user.ctrl");
const WaitingController = require("./DB/schemes/waiting/waiting.ctrl");
const BanConttroller = require("./DB/schemes/banned/banned.ctrl");
const responseHandler = require("./responseHandler");
const dataAccess = require("./DB/dataAccess");
const messages = require("./responseMessages");
module.exports.checkIfbanned = async (sender) => {
  let user = await UserController.find(sender);
  let isBanned = await checkIfUserBanned(user);
  return isBanned;
};
async function checkIfUserBanned(user) {
  let bannedUser = await BanConttroller.checkIfbanned(user);
  if (bannedUser.status) {
    textParticipant(user, messages.youBanned + bannedUser.banEnd);
    return true;
  } else {
    return false;
  }
}
module.exports.replyBack = (body) => {
  if (body.postback) {
    //handleCommands
    handlePostback(body);
  } else if (body.message) {
    handleMessaging(body);
  }
};
async function handleMessaging(body) {
  let sender = await UserController.find(body.sender);
  let isBanned = await checkIfUserBanned(sender);
  if (!isBanned) {
    dataAccess
      .getReceiver(sender)
      .then((receiver) => {
        // if (body && body.message && body.message.text)
        //   console.log(sender.id, body.message.text);
        handleReceiverId(receiver, sender.toObject(), body);
      })
      .catch((err) => {
        console.log(err);
      });
  }
}

function handleReceiverId(receiver, sender, body) {
  //same user , send waiting message

  if (receiver.deviceId == sender.deviceId) {
    textParticipant(
      receiver,
      messages.messages[Math.floor(Math.random() * messages.messages.length)]
    );
  } else if (receiver.justMatched) {
    textBothParticipants(receiver, sender, messages.nowConnected);
    sendUserMessage(receiver, { text: "Hi" });
    sendUserMessage(sender, { text: "Hi" });
  } else {
    // Add null checks for body and body.message to prevent TypeError
    if (body && body.message) {
      sendUserMessage(receiver, body.message);
    } else {
      console.warn("handleReceiverId: body or body.message is undefined", { body, sender: sender.deviceId });
    }
  }
}
function sendUserMessage(user, message) {
  responseHandler.handleMessage(user, {
    ...message,
    source: "user",
  });
}

function textBothParticipants(receiver, sender, text) {
  textParticipant(receiver, text, sender);
  textParticipant(sender, text, receiver);
}
function textParticipant(user, text, partner) {
  if (user) {
    if (partner) {
      let { gender, name, sessionId, avatar } = {
        ...partner,
        ...partner.settings,
      };
      responseHandler.handleMessage(user, {
        text,
        partner: { _id: partner._id, gender, name, sessionId, avatar },
        source: "sys",
      });
    } else {
      responseHandler.handleMessage(user, {
        text,
        source: "sys",
      });
    }
  }
}
/**
 * This is for handling the presaved menu items (reconnect , disconnect , help , getstarted)
 * @param {Object} sender
 * @param {Object} received_postback
 */
// reloading the sender to refresh the user data
// eslint-disable-next-line max-statements
async function handlePostback({ sender, postback }) {
  let dbUser = await UserController.find(sender);
  let user = dbUser.toObject();
  let payload = postback.payload;

  if (payload === "RECONNECT") {
    //remove from chatting add to waiting
    handleReconnect(user);
  } else if (payload === "BLOCK") {
    if (postback.id) {
      console.log(`user deviceId ${user.deviceId} has blocked ${postback.id}`);
      blockUser(user, postback.id);
    } else {
      console.log(`user ${user.deviceId} has blocked someone`);
      handleBlock(user);
    }
  } else if (payload === "REPORT") {
    if (postback.id) {
      console.log(`user deviceId ${user.deviceId} has reported ${postback.id}`);
      handleReport(user, postback.id);
    } else handleBlock(user, true);
  } else if (payload === "DISCONNECT") {
    handleDisconnect(user);
  } else if (payload === "settings") {
    handleSaveSettings(dbUser, postback.settings);
  } else textParticipant(user, messages.help); //help and get_started
  // responseHandler.handlePostback(sender_psid, received_postback);
}
function handleSaveSettings(user, settings) {
  UserController.update(user, settings);
}
function handleDisconnect(user) {
  dataAccess.removeFromChatting(user).then((data) => {
    if (data.participants) {
      //if chatting object not user
      textBothParticipants(
        data.participants[0],
        data.participants[1],
        messages.partnerLeft
      );
    }
    removeUserFromWaiting(user);
  });
}
function blockUser(user, blockedId) {
  if (user.blockList.includes(blockedId)) {
    textParticipant(user, messages.youAlreadyBlocked);
  } else {
    //TODO: need to be moved to the chatting controller findParticipant
    ChattingController.find(user).then((chatting) => {
      if (chatting) {
        let [blockedUser] = chatting.participants.filter((participant) => {
          if (participant.deviceId != user.deviceId) return participant;
        });
        if (blockedUser._id.equals(blockedId)) {
          ChattingController.remove(user);
          textParticipant(blockedUser, messages.partnerLeft);
        }
      }
    });
    UserController.block(user, blockedId);
    textParticipant(user, messages.youBlocked);
  }

  return user;
}
function handleReport(user, reportedId) {
  blockUser(user, reportedId);
  reportUser(user, reportedId);
}
function reportUser(user, reportedId) {
  if (user.reportList.some((voter) => voter.equals(reportedId))) {
    textParticipant(user, messages.youAlreadyReported);
  } else if (user.reportList.length > 10) {
    textParticipant(user, messages.youReported);
  } else {
    UserController.report(user, reportedId).then((reportedUser) => {
      if (reportedUser && reportedUser.lastReported.count % 5 == 0) {
        let banToDate = new Date();
        let daysToBan = [1, 1, 2, 3, 5, 8, 13, 21, 34][reportedUser.banCount];
        banToDate.setDate(banToDate.getDate() + daysToBan);
        BanConttroller.add(reportedUser, banToDate);
        UserController.ban(reportedUser);
        textParticipant(reportedUser, messages.youBanned + banToDate);
      }
    });
    textParticipant(user, messages.youReported);
  }
}

//old version of blocking
function handleBlock(user, reported) {
  dataAccess.removeFromChatting(user).then((data) => {
    if (data.participants) {
      //if chatting object not user
      let [blockedUser] = data.participants.filter((participant) => {
        if (participant.deviceId != user.deviceId) return participant;
      });
      textParticipant(user, messages.youBlocked);
      UserController.block(user, blockedUser);
      if (reported) reportUser(user, blockedUser);

      textParticipant(blockedUser, messages.partnerLeft);
    } else {
      textParticipant(user, messages.connectToBlock);
    }
  });
}

async function handleReconnect(user) {
  let isBanned = await checkIfUserBanned(user);
  if (!isBanned)
    dataAccess.removeFromChatting(user).then((data) => {
      if (data.participants) {
        textBothParticipants(
          data.participants[0],
          data.participants[1],
          messages.partnerLeft
        );
      }
      addUserToWaiting(user);
    });
}

function removeUserFromWaiting(sender) {
  WaitingController.remove(sender).then(() => {
    textParticipant(sender, messages.disconnect);
  });
}

function addUserToWaiting(sender) {
  dataAccess.addUserToWaiting(sender).then((data) => {
    handleReceiverId(data, sender);
  });
}
