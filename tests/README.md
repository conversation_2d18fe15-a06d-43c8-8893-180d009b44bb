# Test Suite for Friendly Chat Application

This directory contains comprehensive unit and integration tests for the Friendly Chat application, a real-time chat application that randomly connects users for conversations.

## Test Structure

### Test Files

- **`setup.js`** - Global test setup and configuration
- **`database.test.js`** - Tests for database models and controllers (User, Chatting, Waiting)
- **`requestHandler.test.js`** - Tests for request handling logic and message routing
- **`responseHandler.test.js`** - Tests for response handling and message delivery
- **`socket.test.js`** - Tests for Socket.IO functionality and real-time communication
- **`utilities.test.js`** - Tests for utility functions and message formatting
- **`googleDrive.test.js`** - Tests for Google Drive image upload functionality
- **`integration.test.js`** - End-to-end integration tests for complete user flows
- **`routes.test.js`** - Tests for HTTP API endpoints and webhook handling
- **`runTests.js`** - Custom test runner with categorized test execution

### Test Categories

#### Unit Tests
- **Database Tests**: CRUD operations, model validation, relationship handling
- **Request Handler Tests**: Message processing, postback handling, user matching logic
- **Response Handler Tests**: Message delivery to different channels (app, Facebook)
- **Utilities Tests**: Message creation functions, response messages, helper functions
- **Google Drive Tests**: Image upload functionality with proper mocking

#### Integration Tests
- **User Matching Flow**: Complete flow from user connection to chat initiation
- **Chat Session Management**: Message exchange between matched users
- **Disconnection Handling**: User disconnect and reconnect scenarios
- **Edge Cases**: Error handling, inactive user cleanup, multiple users

#### API Tests
- **Webhook Endpoints**: Facebook webhook verification and message processing
- **Route Handlers**: Basic endpoint functionality and error responses

#### Socket.IO Tests
- **Real-time Communication**: Socket events, message broadcasting
- **Image Upload**: Chunked image upload and processing
- **Connection Management**: Connect, disconnect, error handling

## Running Tests

### Prerequisites

```bash
npm install
```

### Basic Test Commands

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch

# Clear Jest cache
npm run clear_jest
```

### Categorized Test Execution

```bash
# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run only API tests
npm run test:api

# Run only Socket.IO tests
npm run test:socket
```

### Advanced Test Runner

```bash
# Using the custom test runner
node tests/runTests.js [category] [options]

# Examples:
node tests/runTests.js unit --coverage
node tests/runTests.js integration --watch
node tests/runTests.js all --verbose
```

## Test Configuration

### Environment Setup

Tests use an in-memory MongoDB database for isolation and speed. The setup includes:

- **MongoDB Memory Server**: Provides isolated database instances
- **Mocked External Services**: Google Drive API, Facebook Graph API
- **Test Utilities**: Helper functions for creating test data
- **Environment Variables**: Test-specific configuration

### Coverage Configuration

Coverage is configured to include:
- All scripts in the `scripts/` directory
- Main application files (`app.*.js`, `google.config.js`)
- Excludes node_modules and index files

### Timeout Configuration

Tests have a 30-second timeout to accommodate:
- Database operations
- Socket.IO connection establishment
- Async operations and promises

## Test Data and Mocking

### Test Helpers

Global helper functions available in all tests:

```javascript
// Create test user data
createTestUser(userId, channelID)

// Create test message
createTestMessage(text, userId)

// Create test attachment
createTestAttachment(url, userId)

// Create test postback
createTestPostback(payload, userId)
```

### Mocking Strategy

- **Database**: In-memory MongoDB for real database operations
- **External APIs**: Jest mocks for Google Drive and Facebook APIs
- **Socket.IO**: Real Socket.IO server with client connections
- **HTTP Requests**: Supertest for API endpoint testing

## Key Test Scenarios

### User Matching Flow
1. User sends first message → added to waiting list
2. Second user sends message → matched with first user
3. Both users receive "now connected" message
4. Messages are routed between matched users

### Disconnection Scenarios
1. User disconnects → partner notified, session cleaned up
2. User reconnects → removed from current session, added to waiting
3. Inactive users → automatically cleaned up after 24 hours

### Image Upload Flow
1. Client sends image chunks via Socket.IO
2. Server accumulates chunks by message ID
3. Complete image uploaded to Google Drive
4. Drive URL sent as attachment message

### Error Handling
- Invalid webhook data
- Database connection issues
- External API failures
- Socket connection errors

## Debugging Tests

### Common Issues

1. **Database Connection**: Ensure MongoDB Memory Server is properly initialized
2. **Socket Timeouts**: Check Socket.IO connection establishment
3. **Mock Failures**: Verify mock implementations match actual APIs
4. **Async Operations**: Ensure proper await/promise handling

### Debug Commands

```bash
# Run tests with verbose output
npm test -- --verbose

# Run specific test file
npm test -- tests/database.test.js

# Run tests matching pattern
npm test -- --testNamePattern="should handle user matching"
```

## Contributing to Tests

### Adding New Tests

1. Choose appropriate test file or create new one
2. Follow existing naming conventions
3. Use provided test helpers
4. Mock external dependencies
5. Test both success and error cases

### Test Best Practices

- **Isolation**: Each test should be independent
- **Cleanup**: Use beforeEach/afterEach for setup/teardown
- **Assertions**: Use descriptive expect statements
- **Coverage**: Aim for high code coverage
- **Documentation**: Comment complex test scenarios

### Mock Guidelines

- Mock external services (APIs, file system)
- Use real database for data layer tests
- Mock time-dependent operations
- Provide realistic mock responses
