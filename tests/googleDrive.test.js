// Mock Google Cloud Storage before requiring the module
jest.mock("@google-cloud/storage");

describe("Google Cloud Storage Image Upload", () => {
  let mockStorage;
  let mockBucket;
  let mockFile;
  let uploadImg;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules(); // Reset modules to ensure fresh imports

    mockFile = {
      save: jest.fn(),
      get name() {
        // Return the name based on the file path that was requested
        const calls = mockBucket.file.mock.calls;
        if (calls.length > 0) {
          return calls[calls.length - 1][0]; // Return the last file path requested
        }
        return "imgs/device123-test-image";
      }
    };

    mockBucket = {
      file: jest.fn().mockReturnValue(mockFile),
      name: "friendly-chat-images"
    };

    mockStorage = {
      bucket: jest.fn().mockReturnValue(mockBucket)
    };

    const { Storage } = require("@google-cloud/storage");
    Storage.mockImplementation(() => mockStorage);

    // Require the module after setting up mocks
    const googleConfig = require("../google.config");
    uploadImg = googleConfig.uploadImg;
  });

  describe("uploadImg", () => {
    it("should upload image successfully", async () => {
      mockFile.save.mockResolvedValue();

      const result = await uploadImg("base64imagedata", "device123", "test-image");

      expect(result).toBe("https://storage.googleapis.com/friendly-chat-images/imgs/device123-test-image");

      expect(mockStorage.bucket).toHaveBeenCalledWith("friendly-chat-images");
      expect(mockBucket.file).toHaveBeenCalledWith("imgs/device123-test-image");
      expect(mockFile.save).toHaveBeenCalledWith(expect.any(Buffer));

      // Verify the buffer was created from base64
      const savedBuffer = mockFile.save.mock.calls[0][0];
      expect(savedBuffer).toBeInstanceOf(Buffer);
      // The buffer contains the decoded base64 data, not the original string
      expect(savedBuffer).toEqual(Buffer.from("base64imagedata", "base64"));
    });

    it("should handle file save error", async () => {
      const error = new Error("File save failed");
      mockFile.save.mockRejectedValue(error);

      await expect(uploadImg("base64imagedata", "device123", "test-image"))
        .rejects.toThrow("File save failed");

      expect(mockFile.save).toHaveBeenCalled();
    });

    it("should handle file creation error", async () => {
      mockBucket.file.mockImplementation(() => {
        throw new Error("File creation failed");
      });

      await expect(uploadImg("base64imagedata", "device123", "test-image"))
        .rejects.toThrow("File creation failed");
    });

    it("should handle filename with special characters", async () => {
      mockFile.save.mockResolvedValue();

      const result = await uploadImg("base64imagedata", "device123", "test.image.with.dots");

      expect(result).toBe("https://storage.googleapis.com/friendly-chat-images/imgs/device123-test.image.with.dots");
      expect(mockBucket.file).toHaveBeenCalledWith("imgs/device123-test.image.with.dots");
    });

    it("should handle empty base64 data", async () => {
      mockFile.save.mockResolvedValue();

      const result = await uploadImg("", "device123", "empty-image");

      expect(result).toBe("https://storage.googleapis.com/friendly-chat-images/imgs/device123-empty-image");
      expect(mockFile.save).toHaveBeenCalledWith(expect.any(Buffer));

      // Verify empty buffer was created
      const savedBuffer = mockFile.save.mock.calls[0][0];
      expect(savedBuffer.length).toBe(0);
    });

    it("should handle different device IDs", async () => {
      mockFile.save.mockResolvedValue();

      const result = await uploadImg("base64imagedata", "different-device", "test-image");

      expect(result).toBe("https://storage.googleapis.com/friendly-chat-images/imgs/different-device-test-image");
      expect(mockBucket.file).toHaveBeenCalledWith("imgs/different-device-test-image");
    });

    it("should use correct bucket name", async () => {
      mockFile.save.mockResolvedValue();

      await uploadImg("base64imagedata", "device123", "test-image");

      expect(mockStorage.bucket).toHaveBeenCalledWith("friendly-chat-images");
    });

    it("should create correct file path", async () => {
      mockFile.save.mockResolvedValue();

      await uploadImg("base64imagedata", "device123", "test-image");

      expect(mockBucket.file).toHaveBeenCalledWith("imgs/device123-test-image");
    });

    it("should return correct public URL", async () => {
      mockFile.save.mockResolvedValue();

      const result = await uploadImg("base64imagedata", "device123", "test-image");

      expect(result).toBe("https://storage.googleapis.com/friendly-chat-images/imgs/device123-test-image");
    });
  });

  describe("Buffer handling", () => {
    it("should convert base64 to buffer correctly", async () => {
      mockFile.save.mockResolvedValue();

      await uploadImg("dGVzdA==", "device123", "test-image"); // "test" in base64

      const savedBuffer = mockFile.save.mock.calls[0][0];
      expect(savedBuffer).toBeInstanceOf(Buffer);
      expect(savedBuffer.toString()).toBe("test");
    });

    it("should handle binary data", async () => {
      mockFile.save.mockResolvedValue();

      const binaryData = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="; // 1x1 PNG
      await uploadImg(binaryData, "device123", "test.png");

      const savedBuffer = mockFile.save.mock.calls[0][0];
      expect(savedBuffer).toBeInstanceOf(Buffer);
      expect(savedBuffer.length).toBeGreaterThan(0);
    });
  });
});
