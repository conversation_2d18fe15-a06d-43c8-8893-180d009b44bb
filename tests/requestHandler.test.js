const { replyBack } = require("../scripts/requestHandler");
const dataAccess = require("../scripts/DB/dataAccess");
const responseHandler = require("../scripts/responseHandler");
const messages = require("../scripts/responseMessages");

// Mock dependencies
jest.mock("../scripts/DB/dataAccess");
jest.mock("../scripts/responseHandler");

describe("Request Handler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("replyBack", () => {
    it("should handle postback messages", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "RECONNECT" }
      };

      const mockUser = { userId: "user123", channelID: "channel456" };
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockUser);
      dataAccess.addUserToWaiting = jest.fn().mockResolvedValue({ user: mockUser });

      await replyBack(mockBody);

      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith({
        userId: "user123",
        channelID: "channel456"
      });
    });

    it("should handle text messages", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = { userId: "user456", channelID: "channel456" };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(dataAccess.getReceiver).toHaveBeenCalledWith({
        userId: "user123",
        channelID: "channel456"
      });
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        { text: "Hello world" }
      );
    });

    it("should handle same user messaging (waiting scenario)", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = { userId: "user123", channelID: "channel456" };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: expect.any(String)
        })
      );
      
      // Verify it's one of the waiting messages
      const lastCall = responseHandler.handleMessage.mock.calls[0][1];
      expect(messages.messages).toContain(lastCall.text);
    });

    it("should handle newly matched users", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockReceiver = { 
        userId: "user456", 
        channelID: "channel456",
        justMatched: true 
      };
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(responseHandler.handleMessage).toHaveBeenCalledTimes(3);
      // First two calls should be the "now connected" message to both users
      expect(responseHandler.handleMessage).toHaveBeenNthCalledWith(
        1, mockReceiver, { text: messages.nowConnected }
      );
      expect(responseHandler.handleMessage).toHaveBeenNthCalledWith(
        2, { userId: "user123", channelID: "channel456" }, { text: messages.nowConnected }
      );
      // Third call should be the actual message
      expect(responseHandler.handleMessage).toHaveBeenNthCalledWith(
        3, mockReceiver, { text: "Hello world" }
      );
    });
  });

  describe("Postback handling", () => {
    it("should handle DISCONNECT postback", async () => {
      const mockSender = { userId: "user123", channelID: "channel456" };
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockSender);
      dataAccess.removeFromWaiting = jest.fn().mockResolvedValue({});
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith(mockSender);
      expect(dataAccess.removeFromWaiting).toHaveBeenCalledWith(mockSender);
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockSender,
        { text: messages.disconnect }
      );
    });

    it("should handle RECONNECT postback", async () => {
      const mockSender = { userId: "user123", channelID: "channel456" };
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "RECONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockSender);
      dataAccess.addUserToWaiting = jest.fn().mockResolvedValue({ user: mockSender });
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith(mockSender);
      expect(dataAccess.addUserToWaiting).toHaveBeenCalledWith(mockSender);
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockSender,
        { text: messages.reconnected }
      );
    });

    it("should handle help postback", async () => {
      const mockSender = { userId: "user123", channelID: "channel456" };
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "GET_STARTED" }
      };

      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockSender,
        { text: messages.help }
      );
    });

    it("should handle chatting session with participants", async () => {
      const mockSender = { userId: "user123", channelID: "channel456" };
      const mockOtherUser = { userId: "user456", channelID: "channel456" };
      const mockChattingData = {
        participants: [mockSender, mockOtherUser]
      };
      
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockChattingData);
      dataAccess.removeFromWaiting = jest.fn().mockResolvedValue({});
      responseHandler.handleMessage = jest.fn();

      await replyBack(mockBody);

      // Should message both participants about partner leaving
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockSender,
        { text: messages.partnerLeft }
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockOtherUser,
        { text: messages.partnerLeft }
      );
      
      // Should send disconnect message to the requesting user
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockSender,
        { text: messages.disconnect }
      );
    });
  });
});
