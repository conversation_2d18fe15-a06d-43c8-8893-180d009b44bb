// Set up environment before imports
process.env.NODE_ENV = "test";

// Mock mongoose and database models first
jest.mock("mongoose", () => {
  const mockSchema = jest.fn().mockImplementation(() => ({
    post: jest.fn(),
    pre: jest.fn(),
    index: jest.fn()
  }));

  mockSchema.Types = {
    ObjectId: jest.fn()
  };

  return {
    connect: jest.fn().mockResolvedValue({}),
    connection: {
      readyState: 1,
      close: jest.fn().mockResolvedValue({}),
      collections: {}
    },
    set: jest.fn(),
    model: jest.fn(),
    Schema: mockSchema
  };
});

// Mock User model
const mockUser = {
  _id: "mockUserId",
  sessionId: "mockSessionId",
  channelId: "mockChannelId",
  deviceId: "mockDeviceId",
  blockList: [],
  reportList: [],
  save: jest.fn().mockResolvedValue({}),
  toObject: jest.fn().mockReturnValue({
    _id: "mockUserId",
    sessionId: "mockSessionId",
    channelId: "mockChannelId",
    deviceId: "mockDeviceId",
    blockList: [],
    reportList: []
  })
};

jest.mock("../scripts/DB/schemes/user/user", () => {
  const mockUserConstructor = jest.fn().mockImplementation((data) => ({
    ...mockUser,
    ...data,
    save: jest.fn().mockResolvedValue({ ...mockUser, ...data })
  }));

  // Add static methods with proper chaining
  mockUserConstructor.findOneAndUpdate = jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockUser)
    })
  });

  mockUserConstructor.findOne = jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue(mockUser)
  });

  mockUserConstructor.deleteOne = jest.fn().mockResolvedValue({ deletedCount: 1 });

  return mockUserConstructor;
});

// Mock other database models
jest.mock("../scripts/DB/schemes/chatting/chatting", () => ({
  findOne: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  findOneAndDelete: jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue(null)
  }),
  countDocuments: jest.fn().mockResolvedValue(0)
}));

jest.mock("../scripts/DB/schemes/waiting/waiting", () => ({
  findOne: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  findOneAndUpdate: jest.fn().mockReturnValue({
    populate: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(null)
    })
  }),
  deleteOne: jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue({ deletedCount: 1 })
  }),
  countDocuments: jest.fn().mockResolvedValue(0)
}));

jest.mock("../scripts/DB/schemes/banned/banned", () => ({
  findOne: jest.fn().mockResolvedValue(null),
  findOneAndUpdate: jest.fn().mockResolvedValue(null),
  deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 })
}));

jest.mock("../scripts/DB/schemes/user/settings", () => ({
  findOne: jest.fn().mockResolvedValue(null),
  findOneAndUpdate: jest.fn().mockResolvedValue(null),
  updateOne: jest.fn().mockResolvedValue({}),
  deleteMany: jest.fn().mockResolvedValue({ deletedCount: 0 }),
  aggregate: jest.fn().mockReturnValue({
    exec: jest.fn().mockImplementation((callback) => {
      if (callback) callback(null, []);
      return Promise.resolve([]);
    })
  })
}));

// Mock banned controller
jest.mock("../scripts/DB/schemes/banned/banned.ctrl", () => ({
  checkIfbanned: jest.fn().mockResolvedValue({ status: false }),
  add: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue({})
}));

// Mock the response handler to capture messages
jest.mock("../scripts/responseHandler");

const dataAccess = require("../scripts/DB/dataAccess");
const { replyBack } = require("../scripts/requestHandler");
const responseHandler = require("../scripts/responseHandler");
const UserController = require("../scripts/DB/schemes/user/user.ctrl");
const ChattingController = require("../scripts/DB/schemes/chatting/chatting.ctrl");
const WaitingController = require("../scripts/DB/schemes/waiting/waiting.ctrl");
const messages = require("../scripts/responseMessages");

// Helper functions for creating test data
function createTestUser(sessionId, channelId) {
  return {
    _id: `user_${sessionId}`,
    sessionId: sessionId,
    channelId: channelId,
    deviceId: `device_${sessionId}`,
    blockList: [],
    reportList: [],
    save: jest.fn().mockResolvedValue({}),
    toObject: jest.fn().mockReturnValue({
      _id: `user_${sessionId}`,
      sessionId: sessionId,
      channelId: channelId,
      deviceId: `device_${sessionId}`,
      blockList: [],
      reportList: []
    })
  };
}

function createTestMessage(text, senderId) {
  return {
    sender: {
      id: senderId,
      sessionId: senderId,
      channelId: `channel_${senderId}`,
      deviceId: `device_${senderId}`
    },
    recipient: { id: "" }, // Will be set by individual tests
    timestamp: Date.now(),
    message: { text: text }
  };
}

function createTestPostback(payload, senderId) {
  return {
    sender: {
      id: senderId,
      sessionId: senderId,
      channelId: `channel_${senderId}`,
      deviceId: `device_${senderId}`
    },
    recipient: { id: "" }, // Will be set by individual tests
    timestamp: Date.now(),
    postback: { payload: payload }
  };
}

describe("Integration Tests - Full Application Flow", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock responseHandler with debugging
    responseHandler.handleMessage = jest.fn().mockImplementation((user, message) => {
      console.log('responseHandler.handleMessage called with:', { user, message });
    });

    // Mock UserController methods
    UserController.find = jest.fn().mockImplementation((sender) => {
      console.log('UserController.find called with:', sender);
      const user = createTestUser(sender.sessionId || sender.id, sender.channelId || `channel_${sender.id}`);
      console.log('UserController.find returning:', user);
      return Promise.resolve(user);
    });

    UserController.create = jest.fn().mockImplementation((userData) => {
      const user = createTestUser(userData.sessionId, userData.channelId);
      return Promise.resolve(user);
    });

    // Mock banned controller
    const BannedController = require("../scripts/DB/schemes/banned/banned.ctrl");
    BannedController.checkIfbanned = jest.fn().mockResolvedValue({ status: false });

    // Mock ChattingController methods
    ChattingController.find = jest.fn().mockResolvedValue(null);
    ChattingController.create = jest.fn().mockResolvedValue({});
    ChattingController.remove = jest.fn().mockResolvedValue(null);

    // Mock WaitingController methods
    WaitingController.add = jest.fn().mockImplementation((user) => {
      return Promise.resolve({ user });
    });
    WaitingController.remove = jest.fn().mockResolvedValue({});
    WaitingController.fetch = jest.fn().mockResolvedValue(null);

    // Mock dataAccess methods
    dataAccess.getReceiver = jest.fn().mockImplementation((sender) => {
      console.log('dataAccess.getReceiver called with:', sender);
      // First call returns the same user (waiting scenario)
      // Second call returns a different user (matching scenario)
      if (dataAccess.getReceiver.mock.calls.length === 1) {
        console.log('Returning same user for waiting');
        return Promise.resolve(sender);
      } else {
        const otherUser = createTestUser("user2", "channel2");
        otherUser.justMatched = true;
        console.log('Returning matched user:', otherUser.toObject());
        return Promise.resolve(otherUser.toObject());
      }
    });

    dataAccess.addUserToWaiting = jest.fn().mockImplementation((sender) => {
      return Promise.resolve(sender);
    });

    dataAccess.removeFromChatting = jest.fn().mockResolvedValue(null);
  });

  describe("User Matching Flow", () => {
    it("should handle user messaging and waiting flow", async () => {
      // First user sends a message (should be added to waiting list)
      const message1 = createTestMessage("Hello from user1", "user1");
      message1.recipient.id = "channel1";

      // Call replyBack
      replyBack(message1);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Verify user1 gets a waiting message
      const calls = responseHandler.handleMessage.mock.calls;
      expect(calls.length).toBeGreaterThan(0);

      // Check if any call contains a waiting message
      const hasWaitingMessage = calls.some(call => {
        const user = call[0];
        const message = call[1];
        return user && user.sessionId === "user1" && message && typeof message.text === 'string';
      });

      expect(hasWaitingMessage).toBe(true);
    });

    it("should handle conversation between matched users", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));

      // Mock chatting controller to return a chatting session
      ChattingController.find = jest.fn().mockResolvedValue({
        participants: [user1.toObject(), user2.toObject()]
      });

      // Mock dataAccess to return the other user
      dataAccess.getReceiver = jest.fn().mockImplementation((sender) => {
        if (sender.sessionId === "user1") {
          return Promise.resolve(user2.toObject());
        } else {
          return Promise.resolve(user1.toObject());
        }
      });

      jest.clearAllMocks();

      // User1 sends a message to user2
      const message1 = createTestMessage("How are you?", "user1");
      message1.recipient.id = "channel1";

      await replyBack(message1);

      // Verify user2 receives the message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user2" }),
        expect.objectContaining({ text: "How are you?" })
      );

      jest.clearAllMocks();

      // User2 responds
      const message2 = createTestMessage("I'm good, thanks!", "user2");
      message2.recipient.id = "channel2";

      await replyBack(message2);

      // Verify user1 receives the response
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: "I'm good, thanks!" })
      );
    });
  });

  describe("Disconnection Flow", () => {
    it("should handle user disconnection from chat", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));

      // Mock dataAccess.removeFromChatting to return chatting data
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
        participants: [user1.toObject(), user2.toObject()]
      });

      jest.clearAllMocks();

      // User1 disconnects
      const disconnectMessage = createTestPostback("DISCONNECT", "user1");
      disconnectMessage.recipient.id = "channel1";

      await replyBack(disconnectMessage);

      // Verify both users get the "partner left" message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: messages.partnerLeft })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user2" }),
        expect.objectContaining({ text: messages.partnerLeft })
      );

      // Verify user1 gets the disconnect message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: messages.disconnect })
      );

      // Verify chatting session is removed
      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" })
      );
    });

    it("should handle user reconnection", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));

      // Mock dataAccess.removeFromChatting to return chatting data
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({
        participants: [user1.toObject(), user2.toObject()]
      });

      jest.clearAllMocks();

      // User1 reconnects (wants new partner)
      const reconnectMessage = createTestPostback("RECONNECT", "user1");
      reconnectMessage.recipient.id = "channel1";

      await replyBack(reconnectMessage);

      // Verify both users get the "partner left" message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: messages.partnerLeft })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user2" }),
        expect.objectContaining({ text: messages.partnerLeft })
      );

      // Verify user1 gets a waiting message (since they're added back to waiting)
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );

      // Verify chatting session removal was called
      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" })
      );
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle same user messaging themselves", async () => {
      const user1 = await UserController.create(createTestUser("user1", "channel1"));

      // Mock dataAccess to return the same user (waiting scenario)
      dataAccess.getReceiver = jest.fn().mockResolvedValue(user1.toObject());

      jest.clearAllMocks();

      // User sends message while waiting (should get waiting message)
      const message = createTestMessage("Hello", "user1");
      message.recipient.id = "channel1";

      await replyBack(message);

      // Verify user gets a waiting message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );

      // Verify it's one of the waiting messages
      const call = responseHandler.handleMessage.mock.calls[0];
      expect(messages.messages).toContain(call[1].text);
    });

    it("should handle help request", async () => {
      const helpMessage = createTestPostback("GET_STARTED", "user1");
      helpMessage.recipient.id = "channel1";

      replyBack(helpMessage);

      // Give it a moment for async operations
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify responseHandler was called
      expect(responseHandler.handleMessage).toHaveBeenCalled();

      // Check if any call contains the help message
      const calls = responseHandler.handleMessage.mock.calls;
      const hasHelpMessage = calls.some(call => {
        const user = call[0];
        const message = call[1];
        return user && user.sessionId === "user1" && message && message.text === messages.help;
      });

      expect(hasHelpMessage).toBe(true);
    });

    it("should handle inactive user cleanup", async () => {
      // Create users with old timestamps
      const user1 = await UserController.create(createTestUser("user1", "channel1"));

      // Mock dataAccess to simulate cleanup scenario (no active chat found)
      dataAccess.getReceiver = jest.fn().mockResolvedValue(user1.toObject());

      jest.clearAllMocks();

      // Try to send message (should trigger cleanup and add to waiting)
      const message = createTestMessage("Hello", "user1");
      message.recipient.id = "channel1";

      await replyBack(message);

      // Should get waiting message since inactive users were cleaned up
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );
    });

    it("should handle multiple users in waiting queue", async () => {
      // Mock the sequence: first user waits, second user matches, third user waits
      let callCount = 0;
      dataAccess.getReceiver = jest.fn().mockImplementation((sender) => {
        callCount++;
        if (callCount === 1) {
          // First user - goes to waiting
          return Promise.resolve(sender);
        } else if (callCount === 2) {
          // Second user - matches with first
          const matchedUser = createTestUser("user1", "channel1");
          matchedUser.justMatched = true;
          return Promise.resolve(matchedUser.toObject());
        } else {
          // Third user - goes to waiting
          return Promise.resolve(sender);
        }
      });

      // Add first user to waiting
      const message1 = createTestMessage("Hello from user1", "user1");
      message1.recipient.id = "channel1";
      await replyBack(message1);

      jest.clearAllMocks();

      // Add second user - should match with first
      const message2 = createTestMessage("Hello from user2", "user2");
      message2.recipient.id = "channel2";
      await replyBack(message2);

      // Verify matching occurred
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ sessionId: "user1" }),
        expect.objectContaining({ text: messages.nowConnected })
      );

      jest.clearAllMocks();

      // Add third user - should go to waiting
      const message3 = createTestMessage("Hello from user3", "user3");
      message3.recipient.id = "channel3";
      await replyBack(message3);

      // Should get waiting message
      const waitingCall = responseHandler.handleMessage.mock.calls.find(call =>
        messages.messages.includes(call[1].text)
      );
      expect(waitingCall).toBeDefined();
    });
  });
});
