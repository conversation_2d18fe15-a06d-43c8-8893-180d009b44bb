// Response Handler tests with proper mocking

// Set up environment before imports
process.env.NODE_ENV = "test";
process.env.VERIFY_TOKEN = "test_verify_token";

// Mock external dependencies
jest.mock("request");
jest.mock("../app.socket.io");

const request = require("request");
const socket = require("../app.socket.io");
const responseHandler = require("../scripts/responseHandler");

describe("Response Handler (Mocked)", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("handleMessage", () => {
    it("should handle text messages for app channel", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const message = { text: "Hello world" };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });

    it("should handle text messages for external channels", () => {
      const user = { sessionId: "user123", channelID: "facebook_channel" };
      const message = { text: "Hello world" };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      // Current implementation only uses socket.sendMessageToUser regardless of channel
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });

    it("should handle attachment messages for app channel", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const message = {
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }
      });
    });

    it("should handle attachment messages for external channels", () => {
      const user = { sessionId: "user123", channelID: "facebook_channel" };
      const message = {
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      // Current implementation only uses socket.sendMessageToUser regardless of channel
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "image",
          payload: { url: "https://example.com/image.png" }
        },
        source: undefined
      });
    });

    it("should handle API errors gracefully", () => {
      const user = { sessionId: "user123", channelID: "facebook_channel" };
      const message = { text: "Hello world" };

      socket.sendMessageToUser = jest.fn();

      // Should not throw error
      expect(() => {
        responseHandler.handleMessage(user, message);
      }).not.toThrow();

      // Current implementation uses socket, not HTTP requests
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });

    it("should handle multiple attachment types", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const videoMessage = {
        attachments: [{
          type: "video",
          payload: { url: "https://example.com/video.mp4" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, videoMessage);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "video",
          payload: { url: "https://example.com/video.mp4" }
        }
      });
    });

    it("should handle empty or invalid messages", () => {
      const user = { sessionId: "user123", channelID: "app" };

      socket.sendMessageToUser = jest.fn();

      // Test with empty message - the actual implementation sends the empty object
      responseHandler.handleMessage(user, {});
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {});

      jest.clearAllMocks();

      // Test with null message - this will throw an error in actual implementation
      expect(() => {
        responseHandler.handleMessage(user, null);
      }).toThrow();

      // Test with undefined message - this will throw an error in actual implementation
      expect(() => {
        responseHandler.handleMessage(user, undefined);
      }).toThrow();
    });

    it("should prioritize text over attachments", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const message = {
        text: "Hello world",
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      // The actual implementation sends the entire message object when text is present
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world",
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      });
    });

    it("should handle missing environment variables for external channels", () => {
      const user = { sessionId: "user123", channelID: "unknown_channel" };
      const message = { text: "Hello world" };

      // Don't set environment variable for this channel
      delete process.env.unknown_channel;

      request.mockImplementation((options, callback) => {
        callback(null, { statusCode: 200 }, "OK");
      });

      // Should not throw error even with missing env var
      expect(() => {
        responseHandler.handleMessage(user, message);
      }).not.toThrow();
    });

    it("should handle network errors for external channels", () => {
      const user = { sessionId: "user123", channelID: "facebook_channel" };
      const message = { text: "Hello world" };

      socket.sendMessageToUser = jest.fn();

      // Should not throw error
      expect(() => {
        responseHandler.handleMessage(user, message);
      }).not.toThrow();

      // Current implementation uses socket, not HTTP requests
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });

    it("should handle invalid user data", () => {
      socket.sendMessageToUser = jest.fn();

      // Test with null user - this will throw an error in the actual implementation
      expect(() => {
        responseHandler.handleMessage(null, { text: "Hello" });
      }).toThrow();

      // Test with user missing userId - this should work
      expect(() => {
        responseHandler.handleMessage({ channelID: "app" }, { text: "Hello" });
      }).not.toThrow();

      // Test with user missing channelID - this should work but send to undefined channel
      expect(() => {
        responseHandler.handleMessage({ sessionId: "user123" }, { text: "Hello" });
      }).not.toThrow();
    });

    it("should handle large messages", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const largeText = "A".repeat(10000); // 10KB message
      const message = { text: largeText };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: largeText
      });
    });

    it("should handle special characters in messages", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const message = { text: "Hello 🌍! Special chars: @#$%^&*()_+ 中文 العربية" };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello 🌍! Special chars: @#$%^&*()_+ 中文 العربية"
      });
    });

    it("should handle multiple attachments", () => {
      const user = { sessionId: "user123", channelID: "app" };
      const message = {
        attachments: [
          {
            type: "image",
            payload: { url: "https://example.com/image1.png" }
          },
          {
            type: "image",
            payload: { url: "https://example.com/image2.png" }
          }
        ]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      // Should handle the first attachment
      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "image",
          payload: { url: "https://example.com/image1.png" }
        }
      });
    });
  });
});
