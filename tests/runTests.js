#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");

// Test categories and their corresponding files
const testCategories = {
  unit: [
    "tests/database.test.js",
    "tests/requestHandler.test.js", 
    "tests/responseHandler.test.js",
    "tests/utilities.test.js",
    "tests/googleDrive.test.js"
  ],
  integration: [
    "tests/integration.test.js"
  ],
  api: [
    "tests/routes.test.js"
  ],
  socket: [
    "tests/socket.test.js"
  ]
};

// Parse command line arguments
const args = process.argv.slice(2);
const category = args[0];
const coverage = args.includes("--coverage");
const watch = args.includes("--watch");
const verbose = args.includes("--verbose");

function runJest(testFiles, options = {}) {
  const jestArgs = [];
  
  if (testFiles && testFiles.length > 0) {
    jestArgs.push(...testFiles);
  }
  
  if (options.coverage) {
    jestArgs.push("--coverage");
  }
  
  if (options.watch) {
    jestArgs.push("--watch");
  }
  
  if (options.verbose) {
    jestArgs.push("--verbose");
  }
  
  // Add other Jest options
  jestArgs.push("--detectOpenHandles");
  jestArgs.push("--forceExit");
  
  console.log(`Running Jest with args: ${jestArgs.join(" ")}`);
  
  const jest = spawn("npx", ["jest", ...jestArgs], {
    stdio: "inherit",
    cwd: path.resolve(__dirname, "..")
  });
  
  jest.on("close", (code) => {
    process.exit(code);
  });
  
  jest.on("error", (error) => {
    console.error("Error running Jest:", error);
    process.exit(1);
  });
}

function showHelp() {
  console.log(`
Test Runner for Friendly Chat Application

Usage: node tests/runTests.js [category] [options]

Categories:
  unit        - Run unit tests (database, handlers, utilities)
  integration - Run integration tests (full app flow)
  api         - Run API endpoint tests
  socket      - Run Socket.IO tests
  all         - Run all tests (default)

Options:
  --coverage  - Generate coverage report
  --watch     - Watch for file changes
  --verbose   - Verbose output
  --help      - Show this help

Examples:
  node tests/runTests.js unit
  node tests/runTests.js integration --coverage
  node tests/runTests.js all --watch
  npm test -- unit --coverage
  `);
}

// Main execution
if (args.includes("--help") || args.includes("-h")) {
  showHelp();
  process.exit(0);
}

const options = {
  coverage,
  watch,
  verbose
};

switch (category) {
  case "unit":
    console.log("Running unit tests...");
    runJest(testCategories.unit, options);
    break;
    
  case "integration":
    console.log("Running integration tests...");
    runJest(testCategories.integration, options);
    break;
    
  case "api":
    console.log("Running API tests...");
    runJest(testCategories.api, options);
    break;
    
  case "socket":
    console.log("Running Socket.IO tests...");
    runJest(testCategories.socket, options);
    break;
    
  case "all":
  case undefined:
    console.log("Running all tests...");
    runJest([], options);
    break;
    
  default:
    console.error(`Unknown test category: ${category}`);
    showHelp();
    process.exit(1);
}
