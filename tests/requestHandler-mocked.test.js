// Request Handler tests with proper mocking

// Set up environment before imports
process.env.NODE_ENV = "test";
process.env.VERIFY_TOKEN = "test_verify_token";

// Mock all dependencies
jest.mock("../scripts/DB/dataAccess");
jest.mock("../scripts/responseHandler");
jest.mock("../scripts/DB/schemes/user/user.ctrl");
jest.mock("../scripts/DB/schemes/chatting/chatting.ctrl");
jest.mock("../scripts/DB/schemes/waiting/waiting.ctrl");
jest.mock("../scripts/DB/schemes/banned/banned.ctrl");
jest.mock("mongoose");

const dataAccess = require("../scripts/DB/dataAccess");
const responseHandler = require("../scripts/responseHandler");
const UserController = require("../scripts/DB/schemes/user/user.ctrl");
const ChattingController = require("../scripts/DB/schemes/chatting/chatting.ctrl");
const WaitingController = require("../scripts/DB/schemes/waiting/waiting.ctrl");
const BanController = require("../scripts/DB/schemes/banned/banned.ctrl");
const { replyBack } = require("../scripts/requestHandler");
const messages = require("../scripts/responseMessages");

describe("Request Handler (Mocked)", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock response handler
    responseHandler.handleMessage = jest.fn();

    // Mock UserController
    UserController.find = jest.fn().mockResolvedValue({
      _id: "user123",
      deviceId: "device123",
      channelId: "channel456",
      toObject: () => ({
        _id: "user123",
        deviceId: "device123",
        channelId: "channel456",
        blockList: [],
        reportList: []
      })
    });

    // Mock BanController
    BanController.checkIfbanned = jest.fn().mockResolvedValue({ status: false });

    // Mock other controllers
    ChattingController.find = jest.fn().mockResolvedValue(null);
    ChattingController.remove = jest.fn().mockResolvedValue({});
    ChattingController.create = jest.fn().mockResolvedValue({});
    WaitingController.add = jest.fn().mockResolvedValue({ user: { _id: "user123" } });
    WaitingController.remove = jest.fn().mockResolvedValue({});
    WaitingController.fetch = jest.fn().mockResolvedValue(null);
  });

  describe("replyBack function", () => {
    it("should handle text messages between matched users", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockReceiver = {
        deviceId: "user456",
        justMatched: false
      };

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);

      // replyBack doesn't return a promise, so we need to wait for async operations
      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.getReceiver).toHaveBeenCalledWith(mockSender);
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: "Hello world",
          source: "user"
        })
      );
    });

    it("should handle newly matched users", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockReceiver = {
        deviceId: "user456",
        justMatched: true
      };

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should send "now connected" message to both users
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: messages.nowConnected,
          source: "sys"
        })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.nowConnected,
          source: "sys"
        })
      );
      // Should also send Hi messages to both users
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: "Hi",
          source: "user"
        })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: "Hi",
          source: "user"
        })
      );

      expect(responseHandler.handleMessage).toHaveBeenCalledTimes(4);
    });

    it("should handle user messaging themselves (waiting scenario)", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockReceiver = {
        deviceId: "user123" // Same device ID
      };

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: expect.any(String),
          source: "sys"
        })
      );

      // Verify it's one of the waiting messages
      const lastCall = responseHandler.handleMessage.mock.calls[0][1];
      expect(messages.messages).toContain(lastCall.text);
    });

    it("should handle DISCONNECT postback", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      const mockUser = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockChattingData = {
        participants: [
          { deviceId: "user123" },
          { deviceId: "user456" }
        ]
      };

      UserController.find = jest.fn().mockResolvedValue(mockUser);
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockChattingData);
      WaitingController.remove = jest.fn().mockResolvedValue({});

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith({ deviceId: "user123" });
      expect(WaitingController.remove).toHaveBeenCalledWith({ deviceId: "user123" });

      // Should notify both participants about partner leaving
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.partnerLeft,
          source: "sys"
        })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user456" },
        expect.objectContaining({
          text: messages.partnerLeft,
          source: "sys"
        })
      );

      // Should also send disconnect message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.disconnect,
          source: "sys"
        })
      );

      expect(responseHandler.handleMessage).toHaveBeenCalledTimes(3);
    });

    it("should handle RECONNECT postback", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "RECONNECT" }
      };

      const mockUser = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockChattingData = {
        participants: [
          { deviceId: "user123" },
          { deviceId: "user456" }
        ]
      };
      const mockWaitingData = { deviceId: "user123" };

      UserController.find = jest.fn().mockResolvedValue(mockUser);
      BanController.checkIfbanned = jest.fn().mockResolvedValue({ status: false });
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue(mockChattingData);
      dataAccess.addUserToWaiting = jest.fn().mockResolvedValue(mockWaitingData);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith({ deviceId: "user123" });
      expect(dataAccess.addUserToWaiting).toHaveBeenCalledWith({ deviceId: "user123" });

      // Should notify both participants about partner leaving
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.partnerLeft,
          source: "sys"
        })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user456" },
        expect.objectContaining({
          text: messages.partnerLeft,
          source: "sys"
        })
      );

      // The RECONNECT flow calls handleReceiverId which may trigger additional messages
      expect(responseHandler.handleMessage).toHaveBeenCalledTimes(3);
    });

    it("should handle GET_STARTED postback (help)", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "GET_STARTED" }
      };

      const mockUser = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };

      UserController.find = jest.fn().mockResolvedValue(mockUser);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.help,
          source: "sys"
        })
      );
    });

    it("should handle attachment messages", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: {
          attachments: [{
            type: "image",
            payload: { url: "https://example.com/image.png" }
          }]
        }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockReceiver = {
        deviceId: "user456"
      };

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          attachments: [{
            type: "image",
            payload: { url: "https://example.com/image.png" }
          }],
          source: "user"
        })
      );
    });

    it("should handle errors gracefully", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "Hello world" }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const error = new Error("Database error");

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockRejectedValue(error);

      // Should not throw error - replyBack doesn't return a promise in error cases
      expect(() => replyBack(mockBody)).not.toThrow();

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.getReceiver).toHaveBeenCalledWith(mockSender);
    });

    it("should handle empty or invalid messages", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        message: { text: "" }
      };

      const mockSender = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };
      const mockReceiver = {
        deviceId: "user456"
      };

      UserController.find = jest.fn().mockResolvedValue(mockSender);
      dataAccess.getReceiver = jest.fn().mockResolvedValue(mockReceiver);

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should still call getReceiver and send the message (even if empty)
      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.getReceiver).toHaveBeenCalledWith(mockSender);
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        mockReceiver,
        expect.objectContaining({
          text: "",
          source: "user"
        })
      );
    });

    it("should handle single user disconnect (no partner)", async () => {
      const mockBody = {
        sender: { id: "user123" },
        recipient: { id: "channel456" },
        postback: { payload: "DISCONNECT" }
      };

      const mockUser = {
        deviceId: "user123",
        toObject: jest.fn().mockReturnValue({ deviceId: "user123" })
      };

      UserController.find = jest.fn().mockResolvedValue(mockUser);
      // Return data without participants array (no partner)
      dataAccess.removeFromChatting = jest.fn().mockResolvedValue({ deviceId: "user123" });
      WaitingController.remove = jest.fn().mockResolvedValue({});

      replyBack(mockBody);

      // Wait for async operations to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(UserController.find).toHaveBeenCalledWith({ id: "user123" });
      expect(dataAccess.removeFromChatting).toHaveBeenCalledWith({ deviceId: "user123" });
      expect(WaitingController.remove).toHaveBeenCalledWith({ deviceId: "user123" });

      // When there's no partner, only disconnect message should be sent
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        { deviceId: "user123" },
        expect.objectContaining({
          text: messages.disconnect,
          source: "sys"
        })
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledTimes(1);
    });
  });
});
