import { io } from "socket.io-client";
const backEndUrl = "localhost:3000";
let socket;
const SocketService = {
  init: (deviceId, displayName) => {
    socket = io(backEndUrl, {
      query: { deviceId: deviceId, channelId: displayName },
    });
  },
  ack: () => {
    socket.on("ack", (msg) => {
      console.log("ack", msg.id, "status", MessageStatus.sent);
    });
  },
  //   serverMessage: (addServerMessage) => {
  //     socket.on("serverMessage", (msg) => {
  //       //console.log('serverMessage', msg);
  //       if (msg.text) {
  //         notifyUser(msg.text);
  //         addServerMessage(msg);
  //       } else if (msg.attachment.type === "image") {
  //         notifyUser("You have an Image");
  //         addServerMessage({ uri: msg.attachment.payload.url });
  //       }
  //     });
  //   },
  //   disconnect: (addServerMessage) => {
  //     socket.on("disconnect", () =>
  //       addServerMessage({ text: InitMessage.disconnected })
  //     );
  //   },
  emit: (room, message, callback) => {
    socket.emit(room, message, callback);
  },
  close: () => {
    socket.disconnect();
  },
};
export default SocketService;
