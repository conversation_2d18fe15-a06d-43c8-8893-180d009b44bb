const Client = require("socket.io-client");
const { server } = require("../app.socket.io");
const { replyBack } = require("../scripts/requestHandler");
const { uploadImg } = require("../google.config");

// Mock dependencies
jest.mock("../scripts/requestHandler");
jest.mock("../google.config");

describe("Socket.IO Functionality", () => {
  let clientSocket;
  let serverSocket;
  let httpServer;

  beforeAll((done) => {
    jest.setTimeout(10000); // Increase timeout for socket setup

    httpServer = server.listen(() => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        timeout: 5000,
        forceNew: true
      });

      server.on("connection", (socket) => {
        serverSocket = socket;
        done(); // Call done when server socket is ready
      });

      clientSocket.on("connect", () => {
        console.log("Client socket connected");
      });

      clientSocket.on("connect_error", (error) => {
        console.error("Client socket connection error:", error);
        done(error);
      });
    });
  });

  afterAll((done) => {
    if (clientSocket) {
      clientSocket.close();
    }
    if (httpServer) {
      httpServer.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Chat Message Events", () => {
    it("should handle chat message event", (done) => {
      const testMessage = {
        id: "msg123",
        text: "Hello world"
      };

      // Set a timeout to prevent hanging
      const timeout = setTimeout(() => {
        done(new Error("Test timed out waiting for ack"));
      }, 4000);

      replyBack.mockImplementation((msg) => {
        try {
          expect(msg).toEqual({
            sender: { id: clientSocket.id },
            recipient: { id: "app" },
            message: { text: "Hello world" }
          });
        } catch (error) {
          clearTimeout(timeout);
          done(error);
        }
      });

      clientSocket.on("ack", (ack) => {
        try {
          clearTimeout(timeout);
          expect(ack).toEqual({ id: "msg123" });
          done();
        } catch (error) {
          done(error);
        }
      });

      // Ensure socket is connected before emitting
      if (clientSocket.connected) {
        clientSocket.emit("chat message", testMessage);
      } else {
        clientSocket.on("connect", () => {
          clientSocket.emit("chat message", testMessage);
        });
      }
    });

    it("should handle empty chat message", () => {
      const testMessage = {
        id: "msg123",
        text: ""
      };

      replyBack.mockImplementation((msg) => {
        expect(msg.message).toBeUndefined();
      });

      clientSocket.emit("chat message", testMessage);
    });
  });

  describe("Image Upload Events", () => {
    it("should handle image chunks", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      clientSocket.emit("image", imageData, (response) => {
        expect(response).toBe(1);
        done();
      });
    });

    it("should accumulate image chunks", (done) => {
      const imageData1 = {
        id: "img123",
        index: 1,
        data: "base64part1"
      };
      
      const imageData2 = {
        id: "img123",
        index: 2,
        data: "base64part2"
      };

      let callCount = 0;
      const checkComplete = () => {
        callCount++;
        if (callCount === 2) {
          done();
        }
      };

      clientSocket.emit("image", imageData1, (response) => {
        expect(response).toBe(1);
        checkComplete();
      });

      clientSocket.emit("image", imageData2, (response) => {
        expect(response).toBe(2);
        checkComplete();
      });
    });

    it("should handle image sent event", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      const imageSentData = {
        id: "img123"
      };

      uploadImg.mockResolvedValue("https://example.com/uploaded-image.png");
      replyBack.mockImplementation((msg) => {
        expect(msg).toEqual({
          sender: { id: clientSocket.id },
          recipient: { id: "app" },
          message: {
            attachments: [{
              type: "image",
              payload: { url: "https://example.com/uploaded-image.png" }
            }]
          }
        });
      });

      // First send image data
      clientSocket.emit("image", imageData, () => {
        // Then send image sent event
        clientSocket.emit("imageSent", imageSentData, (response) => {
          expect(response).toBe("image recieved");
          expect(uploadImg).toHaveBeenCalledWith("FriendlyChat", "base64imagedata", "img123");
          done();
        });
      });
    });

    it("should handle image upload failure", (done) => {
      const imageData = {
        id: "img123",
        index: 1,
        data: "base64imagedata"
      };

      const imageSentData = {
        id: "img123"
      };

      uploadImg.mockRejectedValue(new Error("Upload failed"));

      // First send image data
      clientSocket.emit("image", imageData, () => {
        // Then send image sent event
        clientSocket.emit("imageSent", imageSentData, (response) => {
          expect(response).toBe("image recieved");
          expect(uploadImg).toHaveBeenCalled();
          done();
        });
      });
    });
  });

  describe("Connection Events", () => {
    it("should handle disconnect event", () => {
      replyBack.mockImplementation((msg) => {
        expect(msg).toEqual({
          sender: { id: clientSocket.id },
          recipient: { id: "app" },
          postback: { title: "Disconnect", payload: "DISCONNECT" }
        });
      });

      clientSocket.disconnect();
    });

    it("should handle logout event", () => {
      replyBack.mockImplementation((msg) => {
        expect(msg).toEqual({
          sender: { id: clientSocket.id },
          recipient: { id: "app" },
          postback: { title: "Disconnect", payload: "DISCONNECT" }
        });
      });

      clientSocket.emit("logout", "user initiated");
    });

    it("should handle reconnect event", () => {
      replyBack.mockImplementation((msg) => {
        expect(msg).toEqual({
          sender: { id: clientSocket.id },
          recipient: { id: "app" },
          postback: { title: "Reconnect", payload: "RECONNECT" }
        });
      });

      clientSocket.emit("reconnect", "connection restored");
    });

    it("should handle error event", () => {
      replyBack.mockImplementation((msg) => {
        expect(msg).toEqual({
          sender: { id: clientSocket.id },
          recipient: { id: "app" },
          postback: { title: "Reconnect", payload: "RECONNECT" }
        });
      });

      clientSocket.emit("error", "connection error");
    });
  });

  describe("Message Creation Functions", () => {
    it("should create message with text", () => {
      const msg = { text: "Hello world" };
      const senderId = "user123";
      
      // Test the createMsg function indirectly through chat message event
      replyBack.mockImplementation((createdMsg) => {
        expect(createdMsg.sender.id).toBe(senderId);
        expect(createdMsg.recipient.id).toBe("app");
        expect(createdMsg.message.text).toBe("Hello world");
      });

      serverSocket.emit("chat message", msg);
    });

    it("should not create message without text", () => {
      const msg = { id: "msg123" };
      
      replyBack.mockImplementation((createdMsg) => {
        expect(createdMsg.message).toBeUndefined();
      });

      serverSocket.emit("chat message", msg);
    });
  });
});
